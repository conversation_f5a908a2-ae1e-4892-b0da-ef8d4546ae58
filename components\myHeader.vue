<template>
	<view class="grace-header-body" style="background-color:#008AFF;">
		<slot name="gHeader">
			<view class="grace-header-leader grace-nowrap grace-space-between grace-flex-vcenter">
				<view class="grace-header-leader-btns grace-icons icon-arrow-left grace-white grace-bold" 
				hover-class="navigator-hover" @tap="goback"></view>
				<view class="grace-header-leader-line"></view>
				<view class="grace-header-leader-btns grace-icons icon-home2 grace-white" 
				hover-class="navigator-hover"  @tap="gohome"></view>
			</view>
			<view class="grace-header-content grace-flex-center" style="margin-right:50rpx;">
				<text class="grace-header-text grace-ellipsis grace-white" >{{  title }}</text>
			</view>
			<view class="grace-white grace-header-right" @tap="handleTap">{{rightTitle}}</view>
		</slot>
	</view>
	
</template>
<script>
export default {
	props: {
		title:{
			type : String,
			default : "劳动者"
		},
		rightTitle:{ // 不要超过3个字
			type : String,
			default : ""
		},
		rightUrl:{
			type : String,
			default : ""
		},
	},
	data() {
		return {
			
		}
	},
	methods:{
		handleTap : function(){
			if(this.rightUrl) uni.navigateTo({ url: this.rightUrl });
		},
		goback : function () { 
			uni.navigateBack({}); 
		},
		gohome : function(){
			uni.reLaunch({
				url: '/pages/index/index'
			});
			// uni.switchTab({
			// 	url:"../index/index"
			// })
		}
	},
}
</script>
<style>
.grace-header-right{margin-right: 30rpx;display: inline-block;width:3em;text-align: right;font-size: .92em;}
</style>